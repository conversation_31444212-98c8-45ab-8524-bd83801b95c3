body {
  background-color: var(--primary);
}

main {
  padding-top: 100px;
  max-width: 1200px;
  margin: 0 auto;
}

.menu-section {
  margin-bottom: 60px;
}

.menu-section h1 {
  font-family: 'Fredoka', sans-serif;
  font-size: 48px;
  color: var(--secondary);
  text-align: center;
  margin-top: 20px;
  margin-bottom: 0;
}

.section-tagline {
  font-family: 'Fredoka', sans-serif;
  text-align: center;
  color: white;
  font-size: 18px;
  margin-top: 0;
  margin-bottom: 15px;
}

.build-your-own {
  display: flex;
  justify-content: center;
  margin: 15px 0;
}

.build-btn {
  background-color: #4b9939;
  color: white;
  border: none;
  border-radius: 20px;
  padding: 8px 20px;
  font-family: 'Fredoka', sans-serif;
  font-size: 16px;
  cursor: pointer;
}

.allergen-filter-container {
  position: absolute;
  top: 120px;
  right: 20px;
  z-index: 10;
  width: 250px;
}

.allergen-filter {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 10px;
  padding: 15px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.allergen-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  font-family: 'Fredoka', sans-serif;
  font-weight: bold;
  color: var(--secondary);
  cursor: pointer;
}

.allergen-header i.bx-chevron-down {
  transition: transform 0.3s ease;
}

.allergen-header.active i.bx-chevron-down {
  transform: rotate(180deg);
}

.allergen-options {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 8px;
  width: 100%;
  font-family: 'Fredoka', sans-serif;
  font-size: 14px;
  margin-top: 10px;
}

.allergen-warning {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 24px;
  height: 24px;
  background-color: #ff9800;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
}

.allergen-warning i {
  font-size: 16px;
}

.allergen-tooltip {
  visibility: hidden;
  width: 200px;
  background-color: #333;
  color: #fff;
  text-align: center;
  border-radius: 6px;
  padding: 8px;
  position: absolute;
  z-index: 1;
  top: 125%;
  right: 0;
  opacity: 0;
  transition: opacity 0.3s;
  font-size: 14px;
  pointer-events: none;
}

.allergen-warning:hover .allergen-tooltip {
  visibility: visible;
  opacity: 1;
}

.menu-items {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 20px;
}

.menu-item {
  width: 250px;
  background-color: transparent;
  border: none;
  text-align: center;
  margin-bottom: 30px;
  position: relative;
}

.menu-item img {
  width: 100%;
  height: 250px;
  object-fit: cover;
  border-radius: 10px;
  margin-bottom: 10px;
}

.menu-item h3 {
  font-family: 'Fredoka', sans-serif;
  margin: 10px 0 5px;
  color: var(--black);
}

.menu-item p {
  font-family: 'Fredoka', sans-serif;
  margin: 5px 0;
  color: var(--black);
}

.customize-btn {
  background-color: white;
  color: var(--secondary);
  border: none;
  border-radius: 50px;
  padding: 8px 15px;
  margin-top: 10px;
  font-family: 'Fredoka', sans-serif;
  font-size: 14px;
  font-weight: bold;
  cursor: pointer;
  box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.customize-btn:hover {
  background-color: var(--secondary);
  color: white;
}

.empty-favorites {
  width: 100%;
  text-align: center;
  padding: 30px;
  background-color: #f9f9f9;
  border-radius: 8px;
  font-family: 'Fredoka', sans-serif;
  color: #666;
}

.swap-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.swap-modal-content {
  background-color: white;
  border-radius: 10px;
  padding: 20px;
  width: 80%;
  max-width: 400px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.15);
}

.swap-modal h3 {
  margin-top: 0;
  font-family: 'Fredoka', sans-serif;
  color: var(--secondary);
  text-align: center;
}

.swap-options {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin: 20px 0;
}

.swap-option {
  background-color: #f9f9f9;
  border: 1px solid #eee;
  border-radius: 8px;
  padding: 12px;
  font-family: 'Raleway', sans-serif;
  cursor: pointer;
  transition: all 0.2s ease;
}

.swap-option:hover {
  background-color: #f0f0f0;
}

.close-modal {
  background-color: var(--secondary);
  color: white;
  border: none;
  border-radius: 20px;
  padding: 10px 20px;
  font-family: 'Fredoka', sans-serif;
  cursor: pointer;
  display: block;
  margin: 0 auto;
}

.info-icon {
  position: relative;
  cursor: help;
  color: var(--secondary);
}

.info-tooltip {
  visibility: hidden;
  width: 220px;
  background-color: #333;
  color: #fff;
  text-align: center;
  border-radius: 6px;
  padding: 8px;
  position: absolute;
  z-index: 1;
  top: 125%;
  right: 0;
  opacity: 0;
  transition: opacity 0.3s;
  font-size: 14px;
  font-weight: normal;
  pointer-events: none;
}

.info-icon:hover .info-tooltip {
  visibility: visible;
  opacity: 1;
}

@media (max-width: 768px) {
  .menu-items {
    gap: 10px;
  }
  
  .menu-item {
    width: 45%;
  }
}

@media (max-width: 480px) {
  .menu-item {
    width: 100%;
  }
}

@media (max-width: 900px) {
  .allergen-filter-container {
    position: static;
    width: 100%;
    margin-bottom: 20px;
  }
  
  .allergen-filter {
    width: 100%;
  }
}
