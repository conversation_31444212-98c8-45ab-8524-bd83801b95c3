* {
  box-sizing: border-box;
}

#rewardsContent {
  text-align: center;
  margin: 40px auto;
  max-width: 2000px;

  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  position: relative;

}

#message{
  font-family: <PERSON><PERSON>;
  font-size: 28px;
  font-weight: 500;

  margin-top: 120px;
}

#couponContainer{
  width: 80%;

  display: flex;

  justify-content: center;
  align-items: center;
  flex-direction: column;
}


#logoutBtn {
  padding: 10px 20px;
  background-color: var(--secondary);
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;

  font-family: <PERSON>oka;
  letter-spacing: 1px;
  font-size: 20px;
  transition: 0.5s ease;
  margin-top: 20px;

  &:hover{
    background-color: var(--fifth);
  }
}

.coupon-holder{
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  justify-content: center;
  align-items: center;
}

.coupon {
  padding: 10px;
  position: relative;


  border-radius: 10px;
  font-size: 20px;
  margin: 10px;

  height: 180px;
  width: 500px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  font-family: <PERSON>oka;
  letter-spacing: 1px;
  transition: 0.5s ease;

  &:hover{
    background-color: var(--secondary);
    cursor: pointer;
    transform: scale(0.98);
  }

  p{
    margin: 0;
    padding: 0;
  }

  .sub{
    margin-top: 10px;
    font-size: 10px;
  }

}

#couponMessage{
  font-family: Fredoka;
  color: var(--fifth); 
  font-weight: 600;
  letter-spacing: 1px;
  margin-bottom: 20px;

}

.title{
  font-family: fredoka;
  font-size: 38px;
  letter-spacing: 1px;
  font-weight: 600;
  padding: 0;
  margin: 0;
  margin-top: 10px;

  color: var(--black);
}

.card {
  width: 100%;
  height: 100%;
  border-radius: 5px;
  box-shadow: 0 4px 6px 0 rgba(0, 0, 0, 0.2);
  background-color: var(--white);
  padding: 20px 10px;
  position: relative;
}

.main,
.copy-button {
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.card::after {
  position: absolute;
  content: "";
  height: 40px;
  right: -20px;
  border-radius: 40px;
  z-index: 1;
  top: 55px;
  background-color: var(--primary);
  width: 40px;
}

.card::before {
  position: absolute;
  content: "";
  height: 40px;
  left: -20px;
  border-radius: 40px;
  z-index: 1;
  top: 55px;
  background-color: var(--primary);
  width: 40px;
}

.co-img img {
  width: 120px;
  height: 120px;
}

.vertical {
  border-left: 5px dotted black;
  height: 100px;
  position: absolute;
  left: 40%;
}

.content h2 {
  font-size: 18px;
  margin-left: 10px;
  color: var(--secondary);
  text-transform: uppercase;
}

.content p {
  font-size: 16px;
  color: #696969;
  margin-left: 0px;
  text-wrap: wrap;
  width: 99%;
}






/* START MESSAGE */

#message2 {
  font-size: 18px;
  position: relative;

  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  font-family: Fredoka;
  font-size: 20px;
  font-weight: 600;
  letter-spacing: 1px;
  color: var(--black);

  margin-top: 200px;

  width: 1000px;

  .inner-style{
    font-size: 125px;
    filter: drop-shadow(5px 5px 0px var(--white));
    margin-bottom: 10px;
    margin-top: 0;
    padding: 0;

  }

  .logo-pic{
    height: 170px;
    margin-bottom: 0;
    position: absolute;
    top: -130px;
    left: 21%;
  }
  

  .button{
    margin-top: 30px;

    a{
      border-radius: 20px;
      padding: 5px 55px;
      background-color: var(--secondary);
      color: var(--white);
      text-decoration: none;
      transition: all 0.5s ease;
      font-size: 25px;

      &:hover{
        background-color: var(--fourth);
      }
    }
  }
}

@media screen and (max-width: 1000px){

  #rewardsContent {
    text-align: center;
    margin: 0px auto;
    max-width: 500px;
  }

  #message2 {
    font-size: 18px;
    position: relative;

    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    font-family: Fredoka;
    font-size: 20px;
    font-weight: 600;
    letter-spacing: 1px;
    color: var(--black);

    margin-top: 200px;

    width: 500px;

    .inner-style{
      font-size: 90px;
      filter: drop-shadow(5px 5px 0px var(--white));
      margin-bottom: 10px;
      margin-top: 0;
      padding: 0;

    }

    .logo-pic{
      height: 120px;
      margin-right: 20px;
      margin-bottom: 0;
      position: absolute;
      top: -110px;
      left: 10%;
    }
    

    .button{
      margin-top: 30px;

      a{
        border-radius: 20px;
        padding: 5px 55px;
        background-color: var(--secondary);
        color: var(--white);
        text-decoration: none;
        transition: all 0.5s ease;
        font-size: 25px;

        &:hover{
          background-color: var(--fourth);
        }
      }
    }
  }


}

@media screen and (max-width: 600px){


  #message2 {
    .inner-style{
      font-size: 85px;

    }

    .logo-pic{
      height: 110px;
      top: -90px;
      left: 15%;
    }
  }


}