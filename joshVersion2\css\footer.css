.footer {
  padding: 40px 20px;

  height: 500px;
  background-image: url(../pictures/footer-bg.png);
  background-position: bottom;
  background-size: cover;

  font-family: <PERSON><PERSON>;
  color: var(--secondary);
}

.footer-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  max-width: 1500px;
  margin: auto;

  margin-top: 100px;
}

.footer-left {
  max-width: 500px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.footer-left .logo {
  height: 150px;

}

.footer-left p {
  margin-top: 10px;
  font-weight: bold;
  font-size: 28px;
}

.social-icons {
  margin-top: 10px;
  display: flex;
  gap: 15px;
}

.social-icons img {
  padding: 10px;
  border-radius: 0%;
  width: 40px;
  height: 40px;
}

.footer-right {
  display: flex;
  gap: 60px;
  flex-wrap: wrap;
}

.footer-section h3 {
  margin-bottom: 10px;
  font-size: 38px;
  font-weight: bold;
}

.footer-section ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-section ul li {
  margin: 5px 0;
}

.footer-section ul li a {
  color: #000;
  text-decoration: none;
  font-size: 30px;
}

.footer-section ul li a:hover {
  text-decoration: underline;
}






@media screen and (max-width: 600px){
  .footer {
    padding: 0px 0px;
    
    height: 800px;
    background-image: none;

    display: flex;
    align-items: center;
    justify-content: center;

  }


  .footer-container {
    display: flex;
    flex-direction: column;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;
  }

}