<!DOCTYPE html>
<html>
<head>
  <title><PERSON><PERSON><PERSON></title>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <link rel="icon" href="pictures/icon-logo.png" type="image/icon type" />

  <link rel="stylesheet" href="css/about.css" />
  <link rel="stylesheet" href="css/header.css" />
  <link rel="stylesheet" href="css/main.css" />

  <!-- AOS -->
  <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet" />

  <!-- Header icons -->
  <link href="https://cdn.jsdelivr.net/npm/remixicon@4.3.0/fonts/remixicon.css" rel="stylesheet"/>
  <link rel="stylesheet" href="https://unpkg.com/boxicons@latest/css/boxicons.min.css"/>

  <script src="https://unpkg.com/boxicons@2.1.3/dist/boxicons.js"></script>

  <!-- Swiper -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css"/>

  <!-- Remove Leaflet CSS -->
  <!-- <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.3/dist/leaflet.css" /> -->

  <!-- Add Mapbox GL CSS -->
  <link href="https://api.mapbox.com/mapbox-gl-js/v2.15.0/mapbox-gl.css" rel="stylesheet" />

  <!-- Remove Leaflet JS -->
  <!-- <script src="https://unpkg.com/leaflet@1.9.3/dist/leaflet.js"></script> -->

  <!-- Add Mapbox GL JS -->
  <script src="https://api.mapbox.com/mapbox-gl-js/v2.15.0/mapbox-gl.js"></script>


</head>

<body>
  <header>

    <div class="NavandHeader">
      <!-- Header logo -->
      <a id="logo" href="index.html"><img src="pictures/Official-logo (4).png" alt="Frootie Smoothie"></a>

      <!-- Header pages -->
      <ul class="navbar">
        <li><a class="active" id="menu">Menu</a></li>
        <li><a class="active" id="rewards">Rewards</a></li>
        <li><a class="active" id="about">About</a></li>
      </ul>

      <div class="main">
        <i class="bx bx-menu" id="menu-icon"></i>
      </div>
    </div>

    <ul class="navbar1">
      <a class="order-button" href="menu.html">
        ORDER
      </a>

      <button id="loginBtn"><img height="32" width="32" src="pictures/account-circle-line.svg" class="profile-icon"/></button>
      <img id="profileIcon" alt="Profile" />
    
      <a href="cart.html" id="cartIcon" style="position: relative;">
        <i class="bx bx-cart" id="cart"></i>
        <span id="cartCount" class="cart-count">0</span>
      </a>
    </ul>
  </header>

  <div class="about-hero">
    <div class="title" data-aos="fade-up" data-aos-duration="500">
      <h1>About Us</h1>
    </div>

    <div class="video"  data-aos="fade-right" data-aos-duration="500" data-aos-delay="500" >
      <img src="pictures/Untitled design (18).png" alt="">
    </div>

    <img class="bg" src="pictures/about-bg-2.png" alt="" data-aos="fade-left" data-aos-duration="500" data-aos-delay="500">
  </div>


  <div class="map-preview">
    <div id="map" data-aos="fade-left" data-aos-duration="500"></div>

    <div class="text" data-aos="fade-right" data-aos-duration="500">
      <p>Chipotle was born of the radical belief that there is a connection between how food is raised and prepared, and how it tastes. Real is better. Better for You, Better for People, Better for Our Planet. It may be the hard way to do things, but it’s the right way</p>
    </div>
  </div>

  <div class="part3">
    <div class="resource-container" data-aos="fade-up" data-aos-duration="500">
      <div class="resources">
        <h1>Resources Used: </h1>
      </div>

      <div class="list">
        <ul>
          <li><p>HTML</p></li>
          <li><p>CSS</p></li>
          <li><p>JAVASCRIPT</p></li>
          <li><p>GSAP</p></li>
          <li><p>Firebase API</p></li>
          <li><p>Mapbox API</p></li>
          <li><p>Turfs JS</p></li>
          <li><p>AOS</p></li>        
          <li><p>Canva</p></li>
          <li><p>Google Fonts</p></li>
          <li><p><a href="pictures/#">Copyright Checklist</a></p></li>
          <li><p><a href="pictures/#">Work Log</a></p></li>

        </ul>
      </div>

      <div>
        <p>All API's used are open sourced and royalty free <br> All Works shown were created by the team <br> All Images shown are royalty and copyright free</p>
      </div>
    </div>
  </div>











  <!-- Firebase SDKs -->
  <script src="https://www.gstatic.com/firebasejs/9.22.2/firebase-app-compat.js"></script>
  <script src="https://www.gstatic.com/firebasejs/9.22.2/firebase-auth-compat.js"></script>

  <!-- Leaflet JS -->
  <script src="https://unpkg.com/leaflet@1.9.3/dist/leaflet.js"></script>

  <!-- Turf.js for geospatial checks -->
  <script src="https://cdn.jsdelivr.net/npm/@turf/turf@6/turf.min.js"></script>

  <script>
    // Update cart count dynamically -- CART
    const cartCountElement = document.getElementById('cartCount');
    const cart = JSON.parse(localStorage.getItem('cart')) || [];
    const totalItems = cart.reduce((sum, item) => sum + item.quantity, 0);
    cartCountElement.innerText = totalItems;
  </script>

  <!-- AOS -->
  <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>

  <!-- GSAP -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.5/gsap.min.js" ></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.5/ScrollTrigger.min.js" ></script>

  <!--JS LINKS -->
  <script src="js/index.js"></script>
  <script src="js/app.js"></script>



<script>

  AOS.init();
  // Your Mapbox access token here - get one free at https://account.mapbox.com/access-tokens/
  mapboxgl.accessToken = 'pk.eyJ1IjoianQ5Nzg0IiwiYSI6ImNtYXVmNXV2ZTA4b2Eya214bzhhMzlpeGUifQ.sH1M9i1yYRpiNn6pivyJmQ';

  // Initialize map centered on continental US
  const map = new mapboxgl.Map({
    container: 'map',
    style: 'mapbox://styles/mapbox/streets-v12',
    center: [-98.5795, 39.8283], // [lng, lat]
    zoom: 4
  });

  // Add zoom and rotation controls to the map.
  map.addControl(new mapboxgl.NavigationControl());

  // Load US States GeoJSON
  fetch("https://raw.githubusercontent.com/PublicaMundi/MappingAPI/master/data/geojson/us-states.json")
    .then(response => response.json())
    .then(states => {
      const markersNeeded = 200;
      let markersAdded = 0;

      // Turf needs [lng, lat] for points
      function getRandomPoint() {
        const latMin = 24.396308;
        const latMax = 49.384358;
        const lonMin = -125.0;
        const lonMax = -66.93457;
        const lat = Math.random() * (latMax - latMin) + latMin;
        const lon = Math.random() * (lonMax - lonMin) + lonMin;
        return [lon, lat];
      }

      function pointInUS(point) {
        const pt = turf.point(point);
        return states.features.some(feature => turf.booleanPointInPolygon(pt, feature));
      }

      // Add markers only on land inside US states
      while (markersAdded < markersNeeded) {
        const point = getRandomPoint();
        if (pointInUS(point)) {
          const [lon, lat] = point;

          // Create a DOM element for the custom marker (you can style this class in your CSS)
          const el = document.createElement('div');
          el.className = 'custom-marker-icon';
          el.style.width = '20px';
          el.style.height = '20px';
          el.style.backgroundColor = 'rgba(255, 69, 0, 0.8)';
          el.style.borderRadius = '50%';
          el.style.border = '2px solid white';

          // Create marker and add to map
          const marker = new mapboxgl.Marker(el)
            .setLngLat([lon, lat])
            .setPopup(
              new mapboxgl.Popup({ offset: 25 }).setHTML(
                `<b>Frooti Smoothie Location #${markersAdded + 1}</b><br>Lat: ${lat.toFixed(4)}, Lon: ${lon.toFixed(4)}`
              )
            )
            .addTo(map);

          markersAdded++;
        }
      }
    })
    .catch(err => {
      console.error("Error loading US States GeoJSON:", err);
    });
</script>

</body>
</html>
