header{
  position: fixed;
  width: 100%;
  top: 0;
  right: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: space-around;
  padding: 1px 0%;
  transition: all 0.5s ease;
  height: 70px;
}

.NavandHeader{
  display: flex;
  align-items: center;
  width: 80%;
}

#logo{
  color: var(--white);
  text-decoration: none;
  font-family: <PERSON><PERSON>;
  margin-left: 50px;
  transition: all 0.5s ease-in-out;

  letter-spacing: 0.5px;
  font-weight: 600;

  display: flex;
  text-align: center;
  justify-content: center;
  align-items: center;

  img{
    height: 75px;
  }
}

#logo:hover{
  transform: scale(1.02);
  cursor: pointer;
}

.navbar{
  display: flex;
  margin-right: 100px;

  li{
    list-style-type: none;
  }

  a{
    color: var(--secondary);
    font-size: 1.25rem;
    padding: 5px 0;
    margin: 0px 50px;
    transition: all 0.5s ease;
    text-decoration: none;
    letter-spacing: 0.5px;
    font-weight: 600;

    font-family: <PERSON><PERSON>;
  }

  a:hover{
    cursor: pointer;
  }

}

.navbar1{
  display: flex;
  align-items: center;
  justify-content: end;
  gap: 20px;
  width: 250px;
  margin-right: 0px;
}

#loginBtn{
  text-align: center;
  align-items: center;
  justify-content: center;

  border: none;
  text-decoration: none;
  background-color: transparent;
  transform: scale(1.05);

  cursor: pointer;

  filter: brightness(0) saturate(100%) invert(58%) sepia(29%) saturate(3698%) hue-rotate(320deg) brightness(95%) contrast(93%);

  transition: all 0.5s ease;

  &:hover{
    transform: scale(1.1);
    filter: brightness(0) saturate(100%) invert(68%) sepia(13%) saturate(1004%) hue-rotate(256deg) brightness(88%) contrast(85%);
  }
}

.order-button{
  background-color: var(--secondary);
  padding: 8px 55px;
  border-radius: 20px;
  font-family: Raleway;
  font-weight: 600;
  font-size: 18px;
  color: var(--white);
  letter-spacing: 1px;
  text-decoration: none;
  transition: all 0.5s ease;

  &:hover{
    background-color: var(--fourth);
    transform: scale(1.02);
  }
}

.navbar a:hover{
  color: var(--white);
}

.navbar a:active{
  color: rgb(94, 35, 35);
}

.main{
  display: flex;
  align-items: center;
  justify-content: center;
  display: none;
}

.main2{
  display: flex;
  align-items: center;
}

#menu-icon{
  font-size: 35px;
  color: var(--secondary);
  cursor: pointer;
  z-index: 1001;
  display: none;
}


/*cart dispplay - none*/
#cartIcon {
  display: flex;
  align-items: center;
  color: var(--secondary);
  font-size: 1.9rem;
  gap: 5px;
  cursor: pointer;
  text-decoration: none;
  margin-right: 10px;

  transition: all 0.5s ease;
}

#cartIcon:hover {
  color: var(--fourth);
  transform: scale(1.05);
}

.cart-count {
  position: absolute;
  top: -8px;
  right: -10px;
  background-color: var(--fourth);
  color: white;
  font-size: 0.7rem;
  padding: 2px 6px;
  border-radius: 50%;
}


#profileIcon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: none;
  cursor: pointer;
}


@media (max-width: 1400px){
  header{
    padding: 14px;
    height: 40px;
    justify-content: space-between;
  }

  .NavandHeader{
    display: flex;
    width: 60%;
  }

  #logo{
    font-size: 2.25rem;
    text-align: center;
  }

  .navbar{
    margin-right: 0px;
    margin-left: 0px;
    a{
      padding: 5px 0;
      margin: 0px 25px;
    }
  }

  .navbar1{
    margin-right: 50px;
  }

}

@media (max-width: 900px){

  header{
    width: 100%;
    gap: 50px;
  }

  .NavandHeader{
    display: flex;
    width: 40%;
    gap: 20px;
  }

  .navbar1{
    margin-right: 10px;
  }

  #logo{
    margin-left: 50px;
    font-size: 2rem;
  }

  .main{
    display: block;
  }

  #menu-icon{
    display: block;
  }


  .navbar{
    position: absolute;
    top: 100%;
    right: -100%;
    width: 270px;
    height: 350px;
    background-color: var(--black);
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    border-radius: 10px;
    transition: all 0.5s ease;
    margin-right: 20px;

    a{
      display: block;
      margin: 25px 0px;
      padding: 0px 5px;
      transition: all 0.5s ease;

      &:hover{
        color: var(--primary);
        transform: translateX(5px);
      }
    }
  }

  .navbar.open{
    right: 2%;
  }

  .order-button{
    padding: 8px 35px;
    border-radius: 20px;
    font-size: 18px;
    letter-spacing: 0.5px;
    text-decoration: none;
    transition: all 0.5s ease;
  
  }

}



.scrolled{
  background-color: var(--black);
  color: white;
}


@media (max-width: 500px){

  header{
    width: 100%;
    justify-content: space-between;
    gap: 0px;

  }

  #menu-icon{
    display: block;
    margin-right: 0px;
  }

  #logo{
    color: var(--secondary);
    text-decoration: none;
    font-family: Fredoka;
    margin-left: 20px;
    transition: all 0.5s ease-in-out;
  
    letter-spacing: 0.5px;
    font-weight: 600;
  
    img{
      height: 55px;
    }
  }

  .navbar1{
    display: flex;
    align-items: center;
    justify-content: end;
    gap: 10px;
    width: 150px;
    margin-right: 0px;
  }

  .order-button{
    padding: 8px 35px;
    border-radius: 20px;
    font-size: 14px;
  }

}