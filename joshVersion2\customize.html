<!DOCTYPE html>
<html lang="en">
<head>
  <title>Customize - Frooti Smoothie</title>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link rel="icon" href="pictures/icon-logo.png" type="image/icon type">
  <link href='https://unpkg.com/boxicons@2.1.4/css/boxicons.min.css' rel='stylesheet'>
  <link rel="stylesheet" href="css/header.css">
  <link rel="stylesheet" href="css/main.css">
  <link rel="stylesheet" href="css/customize.css">
</head>

<body>
  <header>
    <div class="NavandHeader">
      <a id="logo" href="index.html"><img src="pictures/Official-logo (4).png" alt="Frootie Smoothie"></a>
      <ul class="navbar">
        <li><a class="active" id="menu">Menu</a></li>
        <li><a class="active" id="rewards">Rewards</a></li>
        <li><a class="active" id="about">About</a></li>
      </ul>
      <div class="main">
        <i class="bx bx-menu" id="menu-icon"></i>
      </div>
    </div>

    <ul class="navbar1">
      <a class="order-button" href="menu.html">ORDER</a>
      <button id="loginBtn"><img height="32" width="32" src="pictures/account-circle-line.svg" class="profile-icon"/></button>
      <img id="profileIcon" alt="Profile" />
      <a href="cart.html" id="cartIcon" style="position: relative;">
        <i class="bx bx-cart" id="cart"></i>
        <span id="cartCount" class="cart-count">0</span>
      </a>
    </ul>
  </header>

  <main class="customize-container">
    <div class="customize-header">
      <a href="menu.html" class="back-button"><i class='bx bx-arrow-back'></i></a>
      <button class="favorite-button" id="favoriteBtn"><i class='bx bx-heart'></i></button>
    </div>

    <div class="product-info">
      <div class="product-image-container">
        <img id="productImage" src="" alt="Product Image">
      </div>
      <div class="product-details">
        <h1 id="productName"></h1>
        <p id="productCalories"></p>
        <p id="productDescription"></p>
        <p class="price" id="productPrice"></p>
      </div>
    </div>

    <div class="ingredients-section">
      <h2>Ingredients</h2>
      <div id="ingredientsList" class="ingredients-list">
        <!-- Ingredients will be added dynamically -->
      </div>
    </div>

    <div class="customize-section">
      <h2>Customize Your Sweetness</h2>
      <div class="sweetness-selector">
        <button class="sweetness-btn" data-value="0">0%</button>
        <button class="sweetness-btn" data-value="25">25%</button>
        <button class="sweetness-btn active" data-value="50">50%</button>
        <button class="sweetness-btn" data-value="75">75%</button>
        <button class="sweetness-btn" data-value="100">100%</button>
      </div>
    </div>

    <div class="add-ins-section">
      <h2>ADD-INS <span class="add-ins-count">2x</span></h2>
      <div class="add-ins-grid">
        <div class="add-in-item">
          <img src="pictures/acai.jpg" alt="Acai">
          <p>Acai</p>
          <p class="calories">10 Calories</p>
          <p class="price">+$1.00</p>
        </div>
        <div class="add-in-item">
          <img src="pictures/almonds.jpg" alt="Almonds">
          <p>Almonds</p>
          <p class="calories">40 Calories</p>
          <p class="price">+$1.25</p>
        </div>
      </div>
    </div>

    <div class="supplements-section">
      <h2>ADD SUPPLEMENTS <span class="supplements-count">1x</span></h2>
      <div class="supplements-grid">
        <div class="supplement-item">
          <img src="pictures/whey-protein.jpg" alt="Whey Protein">
          <p>Whey Protein</p>
          <p class="calories">70 Calories</p>
          <p class="price">+$1.50</p>
        </div>
        <div class="supplement-item">
          <img src="pictures/egg-protein.jpg" alt="Egg Protein">
          <p>Egg Protein</p>
          <p class="calories">50 Calories</p>
          <p class="price">+$1.50</p>
        </div>
      </div>
    </div>

    <div class="recommendations-section">
      <h2>ADD EXTRAS</h2>
      <div class="recommendations-grid">
        <!-- Extras will be added dynamically -->
      </div>
    </div>

    <div class="nutrition-info">
      <p>Please consult a physician or health professional before adding a supplement to your smoothie, especially if you have a medical condition, are pregnant, nursing, or taking medications. Not recommended for children.</p>
      <button class="nutrition-info-btn">NUTRITION INFO</button>
    </div>

    <div class="add-to-cart-container">
      <button id="addToCartBtn" class="add-to-cart-btn">ADD TO CART</button>
      <span id="finalPrice" class="final-price">$6.99</span>
    </div>
  </main>

  <!-- Firebase SDKs -->
  <script src="https://www.gstatic.com/firebasejs/9.22.2/firebase-app-compat.js"></script>
  <script src="https://www.gstatic.com/firebasejs/9.22.2/firebase-auth-compat.js"></script>
  <script src="https://www.gstatic.com/firebasejs/9.22.2/firebase-firestore-compat.js"></script>

  <script src="js/customize.js"></script>
  <script src="js/index.js"></script>
  <script src="js/app.js"></script>
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      document.getElementById('addToCartBtn').addEventListener('click', addToCart);
    });
  </script>
</body>
</html>



