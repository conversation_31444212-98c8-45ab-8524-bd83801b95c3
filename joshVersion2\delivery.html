<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <title><PERSON><PERSON><PERSON>moothie</title>
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <link rel="icon" href="pictures/icon-logo.png" type="image/icon type" />

  <link rel="stylesheet" href="css/main.css">

  <!--AOS-->
  <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">

  <!-- Mapbox GL CSS -->
  <link href="https://api.mapbox.com/mapbox-gl-js/v2.15.0/mapbox-gl.css" rel="stylesheet" />

  <!-- Mapbox Geocoder CSS -->
  <link href="https://api.mapbox.com/mapbox-gl-js/plugins/mapbox-gl-geocoder/v5.0.0/mapbox-gl-geocoder.css" rel="stylesheet" />

  <style>

    .delivery{
      display: flex;
      align-items: center;
      justify-content: center;
      height: 90vh;
      width: 100%;
      margin-top: 20px;
    }

    .delivery-container{
      display: flex;
      flex-direction: column;
      width: 30%;
      height: 100%;

      padding: 50px;
    }

    .map-container{
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 20px;

      width: 70%;
      height: 100%;
    }

    h2{
      font-family: Fredoka;
      font-weight: 600;
      font-size: 38px;
      letter-spacing: 1px;
      margin: 0;
      padding: 0;

      margin-top: 50px;
      margin-bottom: 10px;
    }

    #map {
      height: 100%;
      width: 100%;
      border-radius: 10px;
      margin-top: 10px;
    }

    #geocoder-container {
      margin-top: 50px;
      margin-bottom: 20px;

      display: flex;
      align-items: center;
      gap: 8px; /* optional: space between input and button */
    }

    #deliveryType, #validateBtn, #currentLocationBtn {
      margin-top: 10px;
      padding: 8px 12px;
      font-size: 16px;

      font-family: Raleway;
      background-color: var(--white);
      border: 0;
    }

    #validateBtn, #currentLocationBtn {
      border-radius: 20px;
      cursor: pointer;
      transition: 0.5s ease;
    }

    #currentLocationBtn:hover{
      background-color: rgb(211, 211, 211);
    }


    #validateBtn{
      background-color: var(--secondary);
      color: var(--white);

      &:hover{
        background-color: var(--fifth);
      }
    }

    #enterBtn {
      padding: 8px 12px;
      font-size: 16px;
      border-radius: 20px;
      font-family: Raleway;
      background-color: var(--white);
      color: var(--black);
      border: none;
      cursor: pointer;
      transition: background-color 0.3s;
      margin-bottom: 20px;
    }

    #enterBtn:hover {
      background-color: var(--fourth);
      color: white;
    }


  </style>
</head>
<body>

  <div class="delivery">
    <div class="delivery-container">
      <h2>Choose Delivery Option</h2>

      <select id="deliveryType">
        <option value="delivery">Delivery</option>
        <option value="carryout">Pick Up</option>
      </select>

      <div id="geocoder-container"></div>

      <button id="currentLocationBtn">Use Current Location</button>
      <button id="validateBtn">Continue</button>
    </div>


    <div class="map-container">
      <div id="map"></div>
    </div>

  </div>


  <!-- Mapbox GL JS -->
  <script src="https://api.mapbox.com/mapbox-gl-js/v2.15.0/mapbox-gl.js"></script>
  <!-- Mapbox Geocoder JS -->
  <script src="https://api.mapbox.com/mapbox-gl-js/plugins/mapbox-gl-geocoder/v5.0.0/mapbox-gl-geocoder.min.js"></script>

  <script src="js/delivery.js"></script>
</body>
</html>
