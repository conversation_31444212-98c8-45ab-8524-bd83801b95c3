.cart-page{
  display: flex;
  height: 100%;

  align-items: center;
  justify-content: space-around;
}

.card-information{
  height: 500px;
  width: 500px;
  background-color: rgb(255, 0, 43);
}


.cart-information{
  background-color: var(--white);
  max-width: 500px;
  height: 600px;
  border-radius: 10px;

  
  filter: drop-shadow(26px 20px 0px var(--secondary));

}

.cart-information-holder{
  display: flex;
  flex-direction: column;
  justify-content: flex-end;

  padding: 5px;

  height: 100%;
  width: 100%;
  position: relative;
}

.item-line {
  display: flex;
  width: 400px;
  justify-content: space-between;
  margin-bottom: 5px;
}

.item-line2 {
  display: flex;
  width: 460px;
  justify-content: space-between;
  margin-bottom: 5px;
}


.item-name, .item-price {
  font-size: 20px;
  color: var(--black);
}

.cart-item { 
  display: flex; 
  justify-content: space-between; 

  font-family: Fredoka;
  font-size: 20px;

  margin: 0;
  padding: 2px;
}

.cart-container { 
  max-width: 500px; 
  margin: auto; 
  border: none; 

  position: absolute;
  top: 200px;

}

.full-price{
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

#deliveryInfo{
  width: 90%;
  font-family: Raleway;
  margin-bottom: 10px;
  margin-left: 20px;
  font-weight: 500;

  color: var(--black);
}

#total { 
  font-weight: bold; 
  margin-top: 15px; 

  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;

  font-family: Fredoka;
  font-size: 25px;
  font-weight: 500;

  margin-top: 100px;
}

.total-number{
  padding: 0px 20px;
  margin: 0;
  font-weight: 500;
  color: var(--black);
}

.discount-text{
  font-size: 14px;
  margin: 0;
  margin-top: 10px;
  margin-bottom: 10px;
  padding-right: 120px;

}

.abs-delivery{
  position: absolute;
  top: 10px;
}

.remove-coupon-button {
  padding: 8px 16px;
  background-color: #ff4d4d;  
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 10px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  margin-top: 10px;
  margin-bottom: 10px;

  width: 150px;
  font-family: raleway;
}


.changeQty-button{
  margin-left: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;

  button{
    border-radius: 50px;
    border: none;
    height: 20px;
    width: 20px;
    transition: all 0.2s ease-in-out;

    &:hover{
      cursor: pointer;
      background-color: var(--fourth);
    }
  }
}

h2{
  margin-top: 20px;
  margin-left: 20px;
  font-family: Fredoka;
  font-size: 30px;
  font-weight: 500;
  margin-bottom: 0;
  margin-bottom: 30px;

  color: var(--black);
}

.delivery-button{
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 20px;
  margin-top: 10px;
  margin-bottom: 20px;
  color: var(--black);
  font-weight: 800;
  letter-spacing: 1px;
  
  width: 450px;
  border-radius: 30px;
  border: none;
  font-family: Raleway;
  font-size: 14px;
  padding: 10px 20px;

  background-color: rgb(219, 219, 219);
  transition: 0.2s ease-in-out;

  &:hover{
    background-color: rgb(216, 216, 216);
    cursor: pointer;
  }
}

.purchase-button{
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 20px;
  margin-top: 20px;
  margin-bottom: 50px;
  color: var(--black);
    font-weight: 800;
  letter-spacing: 1px;
  
  width: 450px;
  border-radius: 30px;
  border: none;
  font-family: Raleway;
  font-size: 14px;
  padding: 10px 20px;

  background-color: var(--primary);
  transition: 0.4s ease-in-out;
  &:hover{
    background-color: var(--secondary);
    cursor: pointer;
    color: var(--white);
  }
}