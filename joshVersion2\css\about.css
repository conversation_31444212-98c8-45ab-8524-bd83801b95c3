.custom-marker-icon {
  cursor: pointer;
  box-shadow: 0 0 5px rgba(0,0,0,0.3);
}



.about-hero{
  width: 100%;
  height: 900px;
  display: flex;
  flex-direction: column;

  justify-content: center;
  position: relative;


  .title{
    font-family: <PERSON><PERSON>;
    color: var(--secondary);
    font-size: 108px;

    h1{
      margin: 0;
      padding: 0;

      margin-top: 100px;
      margin-left: 100px;

      letter-spacing: 1px;
      filter: drop-shadow(10px 10px 0px #ffececab);
    }
  }

  .video{
    height: 300px;
    width: 500px;
    margin-left: 180px;

    img{
      object-fit: cover;
      height: 100%;
      width: 100%;
    };
  }

  .bg{
    position: absolute;
    z-index: -1;
    height: 100%;
    right: 0;
    top: 100px;
  }
}


.map-preview{
  height: 900px;
  display: flex;
  align-items: center;
  justify-content: space-around;
}

#map {
  width: 50%;
  height: 650px;
  max-width: 850px;
  margin: 0 auto 40px auto;
  border-radius: 16px;
  box-shadow:
    0 10px 15px rgba(199, 97, 96, 0.3),
    0 4px 6px rgba(199, 97, 96, 0.15);
  border: 4px solid var(--white);

}

.text{
  width: 40%;
  height: 80%;

  display: flex;
  align-items: center;
  justify-content: center;
  padding: 25px;

  p{
    font-family: Fredoka;
    font-size: 38px;
    padding-right: 80px;
    color: var(--black);
  }
}

/* Customize Leaflet popup */
.leaflet-popup-content-wrapper {
  background: var(--primary);
  color: var(--black);
  border-radius: 12px;
  font-weight: 600;
  font-size: 15px;
  padding: 12px 18px;
  box-shadow:
    0 6px 8px rgba(232, 104, 100, 0.3);
}

.leaflet-popup-tip {
  background: var(--primary);
}

/* Custom marker icon: circle with secondary color */
.custom-marker-icon {
  width: 22px;
  height: 22px;
  background-color: var(--secondary);
  border: 3px solid var(--primary);
  border-radius: 50%;
  box-shadow: 0 0 8px var(--secondary);
}

.part3{
  height: 900px;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  .resource-container{
    background-color: white;
    height: 70%;
    width: 84%;
    display: flex;
    flex-direction: column;
    padding: 50px;
    border-radius: 20px;

    font-family: Fredoka;
    font-weight: 400;
    letter-spacing: 1px;
    
  }
}


@media (max-width: 1200px){
  .about-hero{
    width: 100%;
    height: 900px;
    display: flex;
    flex-direction: column;

    justify-content: center;
    position: relative;


    .title{
      font-family: Fredoka;
      color: var(--secondary);
      font-size: 78px;

      h1{
        margin: 0;
        padding: 0;

        margin-top: 100px;
        margin-left: 100px;

        letter-spacing: 1px;
        filter: drop-shadow(10px 10px 0px #ffececab);
      }
    }

    .video{
      margin-left: 100px;

      img{
        object-fit: cover;
        height: 100%;
        width: 100%;
      };
    }

    .bg{
      height: 80%;
    }
  }

  .map-preview{
    height: 700px;
    display: flex;
    align-items: center;
    justify-content: space-around;
  }

  #map {
    width: 50%;
    height: 650px;
    max-width: 850px;
    margin: 0 auto 40px auto;
    border-radius: 16px;
    box-shadow:
      0 10px 15px rgba(199, 97, 96, 0.3),
      0 4px 6px rgba(199, 97, 96, 0.15);
    border: 4px solid var(--white);

  }

  .text{
    width: 40%;
    height: 80%;

    display: flex;
    align-items: center;
    justify-content: center;
    padding: 25px;

    p{
      font-family: Fredoka;
      font-size: 28px;
      padding-right: 60px;
      color: var(--black);
    }
  }
}

@media (max-width: 900px){
  .about-hero{
    width: 100%;
    height: 700px;
    display: flex;
    flex-direction: column;

    justify-content: center;
    position: relative;


    .title{
      font-family: Fredoka;
      color: var(--secondary);
      font-size: 58px;

      h1{
        margin: 0;
        padding: 0;

        margin-top: 100px;
        margin-left: 150px;

        letter-spacing: 1px;
        filter: drop-shadow(10px 10px 0px #ffececab);
      }
    }

    .video{
      margin-left: 150px;

      img{
        object-fit: cover;
        height: 100%;
        width: 100%;
      };
    }

    .bg{
      height: 60%;
      right: 15%;
    }
  }

  .map-preview{
    height: 700px;
    display: flex;
    align-items: center;
    justify-content: space-around;
  }

  #map {
    width: 50%;
    height: 650px;
    max-width: 850px;
    margin: 0 auto 40px auto;
    border-radius: 16px;
    box-shadow:
      0 10px 15px rgba(199, 97, 96, 0.3),
      0 4px 6px rgba(199, 97, 96, 0.15);
    border: 4px solid var(--white);

  }

  .text{
    width: 40%;
    height: 80%;

    display: flex;
    align-items: center;
    justify-content: center;
    padding: 25px;

    p{
      font-family: Fredoka;
      font-size: 28px;
      padding-right: 60px;
      color: var(--black);
    }
  }
}

@media (max-width: 600px){
  .about-hero{
    width: 100%;
    height: 600px;
    display: flex;
    flex-direction: column;

    justify-content: center;
    align-items: center;
    position: relative;


    .title{
      font-family: Fredoka;
      color: var(--secondary);
      font-size: 48px;

      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;

      h1{
        margin: 0;
        padding: 0;

        margin-top: 0px;
        margin-left: 0px;

        letter-spacing: 1px;
        filter: drop-shadow(10px 10px 0px #ffececab);
      }
    }

    .video{
      margin-left: 0px;
      height: 200px;
      width: 400px;
      margin-top: 50px;

      img{
        object-fit: cover;
        height: 100%;
        width: 100%;
      };
    }

    .bg{
      height: 80%;
      display: none;
    }
  }

  .map-preview{
    margin-top: 50px;

    height: 1000px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-around;
    overflow: hidden;
  }

  #map {
    width: 90%;
    height: 650px;
    max-width: 850px;
    margin: 0 auto 40px auto;
    border-radius: 16px;
    box-shadow:
      0 10px 15px rgba(199, 97, 96, 0.3),
      0 4px 6px rgba(199, 97, 96, 0.15);
    border: 4px solid var(--white);

  }

  .text{
    width: 90%;
    height: 50%;

    display: flex;
    align-items: center;
    justify-content: center;
    padding: 25px;

    p{
      font-family: Fredoka;
      font-size: 28px;
      padding-right: 0px;
      color: var(--black);
    }
  }

  .part3{
    height: 900px;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .resource-container{
      background-color: white;
      height: 70%;
      width: 74%;
      display: flex;
      flex-direction: column;
      padding: 50px;
      border-radius: 20px;

      font-family: Fredoka;
      font-weight: 400;
      letter-spacing: 1px;
      
    }
  }
}