<!DOCTYPE html>
<html>
<head>

  <title><PERSON><PERSON><PERSON></title>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link rel="icon" href="pictures/icon-logo.png" type="image/icon type">

  <link rel="stylesheet" href="css/index.css">
  <link rel="stylesheet" href="css/header.css">
  <link rel="stylesheet" href="css/main.css">
  <link rel="stylesheet" href="css/footer.css">

  <!--AOS-->
  <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">

  <!-- Header icons -->
  <link href="https://cdn.jsdelivr.net/npm/remixicon@4.3.0/fonts/remixicon.css" rel="stylesheet"/>
  <link rel="stylesheet" href="https://unpkg.com/boxicons@latest/css/boxicons.min.css">

  <script src="https://unpkg.com/boxicons@2.1.3/dist/boxicons.js"></script>

  <!-- Swiper -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css"/>


</head>

<body>
  <header>

    <div class="NavandHeader">
      <!-- Header logo -->
      <a id="logo" href="index.html"><img src="pictures/Official-logo (4).png" alt="Frootie Smoothie"></a>

      <!-- Header pages -->
      <ul class="navbar">
        <li><a class="active" id="menu">Menu</a></li>
        <li><a class="active" id="rewards">Rewards</a></li>
        <li><a class="active" id="about">About</a></li>
      </ul>

      <div class="main">
        <i class="bx bx-menu" id="menu-icon"></i>
      </div>
    </div>

    <ul class="navbar1">
      <a class="order-button" href="menu.html">
        ORDER
      </a>

      <button id="loginBtn"><img height="32" width="32" src="pictures/account-circle-line.svg" class="profile-icon"/></button>
      <img id="profileIcon" alt="Profile" />
    
      <a href="cart.html" id="cartIcon" style="position: relative;">
        <i class="bx bx-cart" id="cart"></i>
        <span id="cartCount" class="cart-count">0</span>
      </a>
    </ul>
  </header>

  <div class="start-page">

    <div class="start-holder">
      <div class="milk-bg"><img src="pictures/bg-1 (1).png" alt=""></div>
      <div class="title">
        <div class="title-text" data-aos="fade-up" data-aos-duration="500" data-aos-delay="500">

          <svg viewBox="0 0 600 200">
            <defs>
              <path id="curve" d="M 50 150 Q 300 90 550 150" />
            </defs>
            <text>
              <textPath href="#curve" startOffset="50%" text-anchor="middle">
                Healthi Living - Made Simpli
              </textPath>
            </text>
          </svg>
        </div>

        <img src="pictures/strawberry.png" alt="" class="fruit1 draggable-fruit">
        <img src="pictures/strawberry.png" alt="" class="fruit2">
        <img src="pictures/banana.png" alt="" class="fruit3">
        <img src="pictures/banana.png" alt="" class="fruit4">
        <img src="pictures/leaf.png" alt="" class="fruit5">
        <img src="pictures/cilantro.png" alt="" class="fruit6">

      </div>

      <div class="cups">
        <div class="cups-holder">
          <img src="pictures/cups-final (1).png" alt="">
        </div>
      </div>

      <div class="bg-writing" data-aos="fade" data-aos-duration="1000">
        <div class="part3 animatable fadeIn">
          <div class="logos">
            <div class="logos-slide">
              <h1> Tasty - </h1>
              <h1> Healthy - </h1>
              <h1> Refreshing - </h1>
              <h1> Bold - </h1>
              <h1> Pure - </h1>
              <h1> Vibrant - </h1>
              <h1> Zesty - </h1>

              <h1> Juicy - </h1>
              <h1> Fresh - </h1>
              <h1> Delight - </h1>
              <h1> Bliss - </h1>
              <h1> Nourish - </h1>
              <h1> Organic - </h1>
            </div>

            <div class="logos-slide">
              <h1> Tasty - </h1>
              <h1> Healthy - </h1>
              <h1> Refreshing - </h1>
              <h1> Bold - </h1>
              <h1> Pure - </h1>
              <h1> Vibrant - </h1>
              <h1> Zesty - </h1>

              <h1> Juicy - </h1>
              <h1> Fresh - </h1>
              <h1> Delight - </h1>
              <h1> Bliss - </h1>
              <h1> Nourish - </h1>
              <h1> Organic - </h1>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="arrow-holder">
    <div class="arrows">
      <img src="pictures/arrow-1.png" alt="" class="arrow1" data-aos="fade-down"  data-aos-anchor=".bowl-img" data-aos-delay="100">
      <img src="pictures/arrow-2.png" alt="" class="arrow2" data-aos="fade-down"  data-aos-anchor=".bowl-img" data-aos-delay="200">
      <img src="pictures/arrow-3.png" alt="" class="arrow3" data-aos="fade-down"  data-aos-anchor=".bowl-img" data-aos-delay="100">
    </div>
  </div>

  <div class="bowls">
    <div class="bowl-holder">
      <div class="bowl1" data-aos="fade-right" data-aos-delay="250" data-aos-duration="800">
        <div class="title"><h1>Drinks</h1></div>
        <div class="bowl-img"><img src="pictures/new-bowl1.png" alt=""></div>
        <div class="bowl-button"><a href="">Blend</a></div>
      </div>

      <div class="bowl2" data-aos="fade-right" data-aos-delay="350" data-aos-duration="800">
        <div class="title"><h1>Bowls</h1></div>
        <div class="bowl-img"><img src="pictures/new-bowl2.png" alt=""></div>
        <div class="bowl-button"><a href="">Mix</a></div>
      </div>

      <div class="bowl3" data-aos="fade-right" data-aos-delay="450" data-aos-duration="800">
        <div class="title"><h1>Salads</h1></div>
        <div class="bowl-img"><img src="pictures/new-bowl3.png" alt=""></div>
        <div class="bowl-button"><a href="">Build</a></div>
      </div>

    </div>

    <div class="gradient"></div>
  </div>

  <div class="about-container">
    <div class="about">
      <div class="text" data-aos="fade-right" data-aos-delay="250" data-aos-duration="800">

        <div class="text-container">
          <div class="title"><h1>Who are We?</h1></div>
          <div class="text1">
            <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
            Ut enim ad minim veniam, quis nostrud exercitationullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit
              in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint </p>
              <br>
              <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
                Ut enim ad minim veniam, quis nostrud exercitationullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit
                in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint </p>
              <br>
              <p>Mr Frooti</p>
              <p>Founder of Frooti Smoothi</p>

            <img class="signature" src="pictures/signature.png" alt="">
          </div>

          <div class="signature"><img src="" alt=""></div>
        </div>
      </div>

      <div class="grid-container-og">
        <div class="grid" data-aos="fade-left" data-aos-delay="250" data-aos-duration="800">
          <div class="grid-container">
            <div class="content">
              <img src="pictures/Screenshot 2025-04-19 141015.png" alt="">
            </div>

            <div class="content pic">
              <img src="pictures/Screenshot 2025-04-19 141015.png" alt="">
            </div>

            <div class="content">
              <img src="pictures/Screenshot 2025-04-19 141015.png" alt="">
            </div>

            <div class="content">
              <img src="pictures/Screenshot 2025-04-19 141015.png" alt="">
            </div>

            <div class="content">
              <img src="pictures/Screenshot 2025-04-19 141015.png" alt="">
            </div>
          </div>
      </div>
      </div>

      <img src="pictures/founder-text.png" alt="" class="abso arrow4" data-aos="fade-right" data-aos-delay="250" data-aos-duration="800" data-aos-anchor=".text1">
      <img src="pictures/team text.png" alt="" class="abso arrow5" data-aos="fade-up" data-aos-delay="250" data-aos-duration="800">
      <img src="pictures/recipe-text.png" alt="" class="abso arrow6" data-aos="fade-right" data-aos-delay="250" data-aos-duration="800">


    </div>
  </div>

  <div class="seperator" data-aos="fade-up" data-aos-delay="250" data-aos-duration="800">
    <div class="seperator-holder">
      <div class="stat1" data-aos="fade-up" data-aos-delay="350" data-aos-duration="800">
        <h1>50</h1>
        <p>Smoothies blended per second across the nation</p>
      </div>

      <div class="stat2" data-aos="fade-up" data-aos-delay="450" data-aos-duration="800">
        <h1>150</h1>
        <p>satisfied customers say they "crave our smoothie daily"</p>
      </div>

      <div class="stat3" data-aos="fade-up" data-aos-delay="550" data-aos-duration="800">
        <h1>250</h1>
        <p>million smoothies served per year</p>
      </div>
    </div>
  </div>

  <div class="standing-smoothie">

    <div class="slider1" data-aos="fade-up" data-aos-delay="250" data-aos-duration="800">

      <div class="slider-content">
        <div class="title">
          <h1>Farm-to-table <br> Suppliers</h1>
        </div>  
  
        <div class="slider-holder">
          <div class="slide-suppliers">
            <img src="pictures/bowl.png" alt="">
            <img src="pictures/bowl.png" alt="">
            <img src="pictures/bowl.png" alt="">
            <img src="pictures/bowl.png" alt="">
            <img src="pictures/bowl.png" alt="">
            <img src="pictures/bowl.png" alt="">
          </div>
          <div class="slide-suppliers">
            <img src="pictures/bowl.png" alt="">
            <img src="pictures/bowl.png" alt="">
            <img src="pictures/bowl.png" alt="">
            <img src="pictures/bowl.png" alt="">
            <img src="pictures/bowl.png" alt="">
            <img src="pictures/bowl.png" alt="">
          </div>
        </div>
      </div>

    </div>

    <div class="slider2" data-aos="fade-down" data-aos-delay="250" data-aos-duration="800">

      <div class="slider-content">
        <div class="title">
          <h1>Sponsors</h1>
        </div>  
  
        <div class="slider-holder">
          <div class="slide-suppliers">
            <img src="pictures/bowl.png" alt="">
            <img src="pictures/bowl.png" alt="">
            <img src="pictures/bowl.png" alt="">
            <img src="pictures/bowl.png" alt="">
            <img src="pictures/bowl.png" alt="">
            <img src="pictures/bowl.png" alt="">
          </div>
          <div class="slide-suppliers">
            <img src="pictures/bowl.png" alt="">
            <img src="pictures/bowl.png" alt="">
            <img src="pictures/bowl.png" alt="">
            <img src="pictures/bowl.png" alt="">
            <img src="pictures/bowl.png" alt="">
            <img src="pictures/bowl.png" alt="">
          </div>
        </div>
      </div>

      <div class="order-now">
        <a href="menu.html">Order now <span>&#8594;</span> </a>
      </div>

    </div>

    <img src="pictures/smoothie-big2.png" alt="" class="abs-smoothie" data-aos="fade-up" data-aos-delay="0" data-aos-duration="800">
    <img src="pictures/splash thing.png" class="splash" data-aos="fade-up" alt="" data-aos-anchor="seperator">
  </div>


<footer class="footer">
  <div class="footer-container">
    <div class="footer-left">
      <img src="pictures/Official-logo (4).png" alt="Footie Smoothi Logo" class="logo" />
      <p class="follow-us">Follow Us</p>
      <div class="social-icons">
        <a href="#"><img src="pictures/instagram (1).png" alt="Instagram" /></a>
        <a href="#"><img src="pictures/youtube (1).png" alt="YouTube" /></a>
        <a href="#"><img src="pictures/twitter (1).png" alt="Twitter" /></a>
      </div>
    </div>
    <div class="footer-right">
      <div class="footer-section">
        <h3>OUR LINKS</h3>
        <ul>
          <li><a href="index.html">Home</a></li>
          <li><a href="about.html">About Us</a></li>
          <li><a href="menu.html">Menu</a></li>
          <li><a href="reward.html">Rewards</a></li>
          <li><a href="cart.html">Cart</a></li>
        </ul>
      </div>
    </div>
  </div>
</footer>


  <!-- Firebase SDKs -->
  <script src="https://www.gstatic.com/firebasejs/9.22.2/firebase-app-compat.js"></script>
  <script src="https://www.gstatic.com/firebasejs/9.22.2/firebase-auth-compat.js"></script>
  <script src="https://www.gstatic.com/firebasejs/9.22.2/firebase-firestore-compat.js"></script>

  <script>
    // Update cart count dynamically -- CART
    const cartCountElement = document.getElementById('cartCount');
    const cart = JSON.parse(localStorage.getItem('cart')) || [];
    const totalItems = cart.reduce((sum, item) => sum + item.quantity, 0);
    cartCountElement.innerText = totalItems;
  </script>

  <!-- AOS -->
  <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>

  <!-- GSAP -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.5/gsap.min.js" ></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.5/ScrollTrigger.min.js" ></script>


  <script>
    AOS.init();
  </script>

  <script>
    gsap.registerPlugin(ScrollTrigger)


    gsap.fromTo('.fruit1', {
      x: -0,
      y: 0,
    },
    {
      y: 300,
      x: 100,
      opacity: 0,
      rotation: 0,
      duration: 5,
      scrollTrigger: {
        trigger: ".cups",
        start: "40% 50%",
        end: "80% 50%",
        scrub: 1,

      }
    })

    gsap.fromTo('.fruit2', {
      x: -0,
      y: 0,
    },
    {
      y: 300,
      x: 100,
      opacity: 0,
      rotation: 0,
      duration: 5,
      scrollTrigger: {
        trigger: ".cups",
        start: "40% 50%",
        end: "80% 50%",
        scrub: 1,

      }
    })

    gsap.fromTo('.fruit3', {
      x: -0,
      y: 0,
    },
    {
      y: 300,
      opacity: 0,
      rotation: 0,
      duration: 5,
      scrollTrigger: {
        trigger: ".cups",
        start: "40% 50%",
        end: "80% 50%",
        scrub: 1,

      }
    })

    gsap.fromTo('.fruit4', {
      x: -0,
      y: 0,
    },
    {
      y: 300,
      opacity: 0,
      rotation: 0,
      duration: 5,
      scrollTrigger: {
        trigger: ".cups",
        start: "40% 50%",
        end: "80% 50%",
        scrub: 1,

      }
    })

    gsap.fromTo('.fruit5', {
      x: -0,
      y: 0,
    },
    {
      y: 300,
      x: -100,
      opacity: 0,
      rotation: 0,
      duration: 5,
      scrollTrigger: {
        trigger: ".cups",
        start: "40% 50%",
        end: "80% 50%",
        scrub: 1,

      }
    })

    gsap.fromTo('.fruit6', {
      x: -0,
      y: 0,
    },
    {
      y: 300,
      x: -100,
      opacity: 0,
      rotation: 0,
      duration: 5,
      scrollTrigger: {
        trigger: ".cups",
        start: "40% 50%",
        end: "80% 50%",
        scrub: 1,

      }
    })

    gsap.fromTo('.fruit1',{
      y: 300,
      x: 100,
      opacity: 0,
    },
    {
      y: 0,
      x: 0,
      opacity: 1,   
    })

    gsap.fromTo('.fruit2',{
      y: 300,
      x: 100,
      opacity: 0,
    },
    {
      y: 0,
      x: 0,
      opacity: 1,
      
    })

    gsap.fromTo('.fruit3',{
      y: 300,
      opacity: 0,
    },
    {
      y: 0,
      x: 0,
      opacity: 1,
      
    })

    gsap.fromTo('.fruit4',{
      y: 300,
      opacity: 0,
    },
    {
      y: 0,
      x: 0,
      opacity: 1,
      
    })

    gsap.fromTo('.fruit5',{
      y: 300,
      x: -100,
      rotation: 0, 
    },
    {
      y: 0,
      x: 0,
      opacity: 1,
      
    })

    gsap.fromTo('.fruit6',{
      y: 300,
      x: -100,
      opacity: 0,
    },
    {
      y: 0,
      x: 0,
      opacity: 1,
      
    })

  </script>
  



  <!--JS LINKS -->
  <script src="js/index.js"></script>
  <script src="js/app.js"></script>


</body>
</html>